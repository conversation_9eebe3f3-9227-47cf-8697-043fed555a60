<template>
  <view>
    <view class="clerkInfoCollect-panel">
      <view class="button-groups">
        <!-- 业绩达成排行榜-->
        <view class="button-item">
          <view class="header-title">
            <view>
              <span class="name">{{ userObj.name }}</span>
              <span class="user-tag"> {{ userObj.postName }}</span>
            </view>
          </view>
          <view class="area">{{ userObj.businessUnitIdName }}</view>
        </view>

        <view class="button-item" style="padding-top: 0">
          <view class="first-title">
            <view class="from-title">报告周期</view>
            <view>{{ timeWeek }}</view>
          </view>
          <view class="table">
            <view class="header">
              <view class="name">重点工作</view>
              <view>周期目标</view>
              <view>实际达成</view>
              <view>完成率</view>
            </view>

            <view class="tableData" v-for="(e, i) in tableList" :key="i">
              <view class="name title">{{ e.taskTypeName }}</view>

              <view>{{ e.targetNum }}</view>
              <view>{{ e.completeNum }}</view>
              <view>{{ e.rate }}%</view>
            </view>
          </view>

          <view class="from-title">问题及反馈</view>

          <uv-textarea
            disabled
            v-model="comment"
            height="250rpx"
            placeholder=""
            :customStyle="customStyle"
            maxlength="500"
          ></uv-textarea>
        </view>
        <view style="display: flex; justify-content: flex-end">
          <image
            style="width: 186rpx; height: 56rpx"
            src="./static/save.svg"
            mode=""
            @tap="savePicture"
          ></image>
        </view>
      </view>
      <tabbarVue tabbarSelect="work"></tabbarVue>
    </view>

    <!-- 保存图片弹窗 -->
    <view v-if="showSaveModal" class="save-modal-overlay" @tap="closeSaveModal">
      <view class="save-modal" @tap.stop>
        <!-- 弹窗内容区域 -->
        <view id="saveContent" class="save-content">
          <!-- <view class="modal-header">
            <view class="modal-title">工作报告详情</view>
          </view> -->

          <view class="modal-user-info">
            <view class="modal-header-title">
              <view>
                <span class="modal-name">{{ userObj.name }}</span>
                <span class="modal-user-tag"> {{ userObj.postName }}</span>
              </view>
            </view>
            <view class="modal-area">{{ userObj.businessUnitIdName }}</view>
          </view>

          <view class="modal-report-period">
            <view class="modal-from-title">报告周期</view>
            <view class="modal-time">{{ timeWeek }}</view>
          </view>

          <view class="modal-table">
            <view class="modal-table-header">
              <view class="modal-table-name">重点工作</view>
              <view>周期目标</view>
              <view>实际达成</view>
              <view>完成率</view>
            </view>

            <view class="modal-table-data" v-for="(e, i) in tableList" :key="i">
              <view class="modal-table-name modal-table-title">{{
                e.taskTypeName
              }}</view>
              <view>{{ e.targetNum }}</view>
              <view>{{ e.completeNum }}</view>
              <view>{{ e.rate }}%</view>
            </view>
          </view>

          <view class="modal-feedback">
            <view class="modal-from-title">问题及反馈</view>
            <view class="modal-comment">{{ comment }}</view>
          </view>
        </view>

        <!-- 弹窗底部操作区 -->
        <view class="modal-actions">
          <view style="display: flex">
            <image
              style="width: 48rpx; height: 48rpx;m"
              src="./static/close.svg"
              @tap="closeSaveModal"
            ></image>
            <image
              style="width: 178rpx; height: 48rpx"
              src="./static/saveX.svg"
              @tap="saveToAlbum"
            ></image>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import tabbarVue from "/components/tabbar/tabbar.vue";
import { ref } from "vue";
import { getOtcWeekReportDetail } from "../tool/api";

const comment = ref("");
const customStyle = {
  background: "rgba(255,255,255,0.9)",
  fontSize: "10px",
};

const tableList = ref();
const timeWeek = ref("");
const userObj = ref({});

// 弹窗控制
const showSaveModal = ref(false);
import { onLoad } from "@dcloudio/uni-app";

onLoad((option) => {
  const obj = uni.getStorageSync("USER__INFO__");
  userObj.value = {
    businessUnitIdName: obj.businessUnitIdName,
    postName: obj.postName,
    name: obj.name,
  };
  return getOtcWeekReportDetail({ id: option.id })
    .then((res) => {
      if (res.code === 0 && res.data) {
        tableList.value = res.data.OtcWeekReportTaskDetailVos.map((e) => {
          return {
            ...e,
            targetNum: e.targetNum || 0,
          };
        });
        comment.value = res.data.comment;
        let endDay = res.data.endDay.replaceAll("-", ".");
        let startDay = res.data.startDay.replaceAll("-", ".");
        timeWeek.value = startDay + "-" + endDay;
      }
    })
    .catch((error) => {
      console.error("Error:", error);
      throw error; // 将错误抛出，供外部捕获
    });
});
// 保存图片 - 显示弹窗
const savePicture = () => {
  showSaveModal.value = true;
};

// 关闭弹窗
const closeSaveModal = () => {
  showSaveModal.value = false;
};

// 保存至相册
const saveToAlbum = () => {
  // 使用uni-app的截屏功能
  uni.showLoading({
    title: "正在生成图片...",
  });

  // 延迟一下确保弹窗完全显示
  setTimeout(() => {
    // 使用截屏API
    if (typeof plus !== "undefined") {
      // App环境
      plus.screen.capture(
        (path) => {
          plus.gallery.save(
            path,
            () => {
              uni.hideLoading();
              uni.showToast({
                title: "保存成功",
                icon: "success",
              });
              closeSaveModal();
            },
            (err) => {
              uni.hideLoading();
              console.error("保存失败:", err);
              uni.showToast({
                title: "保存失败",
                icon: "none",
              });
            }
          );
        },
        (err) => {
          uni.hideLoading();
          console.error("截屏失败:", err);
          uni.showToast({
            title: "截屏失败",
            icon: "none",
          });
        }
      );
    } else {
      // 小程序环境 - 提示用户手动截屏保存
      uni.hideLoading();
      uni.showModal({
        title: "提示",
        content: "请长按屏幕截图保存图片到相册",
        showCancel: false,
        confirmText: "知道了",
        success: () => {
          // 不关闭弹窗，让用户可以截屏
        },
      });
    }
  }, 500);
};
</script>

<style scoped lang="scss">
@import "/static/sass/global.scss";
.header-title {
  margin-bottom: 10rpx;
  .name {
    @include setBoldFont(28rpx, 36rpx, #1d1d1d);
  }
  .user-tag {
    @include setBoldFont(20rpx, 38rpx, $uni-text-color-inverse);
    border-radius: 8rpx;
    height: 38rpx;
    min-width: 50rpx;
    margin-left: 20rpx;
    text-align: center;
    padding: 3rpx 8rpx;
    background: #21c369;
  }
}

.area {
  color: var(---Gray6, #8d9094);
  font-size: 20rpx;
}

.clerkInfoCollect-panel {
  @include globalPageStyle();
  padding: 28rpx 20rpx 0;
  overflow-y: scroll;

  .button-groups {
    margin-bottom: 20rpx;

    .button-item {
      @include cardBgCommonStyle();
      margin-bottom: 20rpx;
      overflow: hidden;

      .setting-text {
        @include setlightFont();
      }

      padding: 20rpx;
    }
  }
}

.first-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24rpx;
  color: #000;
}

.from-title {
  @include setBoldFont(28rpx, 44rpx, #2f3133);
  margin: 20rpx 0;
}

.table {
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.9);
  padding: 20rpx;
  padding-top: 0;
  /* 橙色投影 */
  border: 1px solid #eeeff0;
}

.header,
.tableData {
  display: flex;
  justify-content: space-around;
  text-align: center;
  gap: 18rpx;
  color: var(---Gray6, #8d9094);
  font-size: 24rpx;
  line-height: 80rpx;

  view {
    flex: 1;
  }

  .name {
    flex: 1;
    color: var(---Gray6, #8d9094) !important;
    font-size: 24rpx !important;
    text-align: left;
  }

  .title {
    text-align: left;
  }
}

.tableData {
  @include setBoldFont(28rpx, 36rpx, #2f3133);
  line-height: 40px;
}

.btnNext {
  height: 82rpx;
  text-align: center;
  background: linear-gradient(180deg, #ffbf1a -33.33%, #e55f14 131.94%);
  //box-shadow: 0.0625rem 0.0625rem 0.25rem 0 #f3c66a;
  border-radius: 5px;
  @include absoluteHorizontalCenter();
  bottom: calc(env(safe-area-inset-bottom) + 140rpx);
  width: calc(100% - 30rpx);
  @include setBoldFont(28rpx, 82rpx, #fff);
  z-index: 10000;
}

.nextTurn {
  background: #ffffff;
  color: #f3c66a;
  box-shadow: 0.0625rem 0.0625rem 0.25rem 0 #f3c66a;
  bottom: calc(env(safe-area-inset-bottom) + 240rpx);
}

/* 保存图片弹窗样式 */
.save-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.save-modal {
  border-radius: 16rpx;
  width: 95%;
  max-width: 95vw;
  max-height: 95vh;
  overflow: hidden;
  position: relative;
}

.save-content {
  padding: 40rpx;
  max-height: 80vh;
  overflow-y: auto;
  background: linear-gradient(135deg, #ff9a56 0%, #ffad56 100%);
}

.modal-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.modal-title {
  @include setBoldFont(32rpx, 44rpx, #fff);
}

.modal-user-info {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.modal-header-title {
  margin-bottom: 10rpx;
  .modal-name {
    @include setBoldFont(28rpx, 36rpx, #1d1d1d);
  }
  .modal-user-tag {
    @include setBoldFont(20rpx, 38rpx, #fff);
    border-radius: 8rpx;
    height: 38rpx;
    min-width: 50rpx;
    margin-left: 20rpx;
    text-align: center;
    padding: 3rpx 8rpx;
    background: #21c369;
  }
}

.modal-area {
  color: #8d9094;
  font-size: 20rpx;
}

.modal-report-period {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-from-title {
  @include setBoldFont(28rpx, 44rpx, #2f3133);
}

.modal-time {
  font-size: 24rpx;
  color: #000;
}

.modal-table {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border: 1px solid #eeeff0;
}

.modal-table-header,
.modal-table-data {
  display: flex;
  justify-content: space-around;
  text-align: center;
  gap: 18rpx;
  color: #8d9094;
  font-size: 24rpx;
  line-height: 80rpx;

  view {
    flex: 1;
  }

  .modal-table-name {
    flex: 1;
    color: #8d9094 !important;
    font-size: 24rpx !important;
    text-align: left;
  }

  .modal-table-title {
    text-align: left;
  }
}

.modal-table-data {
  @include setBoldFont(28rpx, 36rpx, #2f3133);
  line-height: 40px;
}

.modal-feedback {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12rpx;
  padding: 20rpx;
}

.modal-comment {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8rpx;
  padding: 20rpx;
  min-height: 200rpx;
  font-size: 24rpx;
  color: #2f3133;
  line-height: 1.5;
  margin-top: 10rpx;
}

.modal-actions {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
  background: transparent;
  margin-top: 20rpx;
}
</style>
