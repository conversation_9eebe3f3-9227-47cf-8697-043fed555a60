<template>
  <view>
    <view class="clerkInfoCollect-panel">
      <view class="button-groups">
        <!-- 业绩达成排行榜-->
        <view class="button-item">
          <view class="header-title">
            <view>
              <span class="name">{{ userObj.name }}</span>
              <span class="user-tag"> {{ userObj.postName }}</span>
            </view>
          </view>
          <view class="area">{{ userObj.businessUnitIdName }}</view>
        </view>

        <view class="button-item" style="padding-top: 0">
          <view class="first-title">
            <view class="from-title">报告周期</view>
            <view>{{ timeWeek }}</view>
          </view>
          <view class="table">
            <view class="header">
              <view class="name">重点工作</view>
              <view>周期目标</view>
              <view>实际达成</view>
              <view>完成率</view>
            </view>

            <view class="tableData" v-for="(e, i) in tableList" :key="i">
              <view class="name title">{{ e.taskTypeName }}</view>

              <view>{{ e.targetNum }}</view>
              <view>{{ e.completeNum }}</view>
              <view>{{ e.rate }}%</view>
            </view>
          </view>

          <view class="from-title">问题及反馈</view>

          <uv-textarea
            disabled
            v-model="comment"
            height="250rpx"
            placeholder=""
            :customStyle="customStyle"
            maxlength="500"
          ></uv-textarea>
        </view>
        <view style="display: flex; justify-content: flex-end">
          <image
            style="width: 186rpx; height: 56rpx"
            src="./static/save.svg"
            mode=""
            @tap="savePicture"
          ></image>
        </view>
      </view>
      <tabbarVue tabbarSelect="work"></tabbarVue>
    </view>
  </view>
</template>

<script setup>
import tabbarVue from "/components/tabbar/tabbar.vue";
import { ref } from "vue";
import { getOtcWeekReportDetail } from "../tool/api";

const comment = ref("");
const id = ref("");
const customStyle = {
  background: "rgba(255,255,255,0.9)",
  fontSize: "10px",
};

const tableList = ref();
const timeWeek = ref("");

const params = ref({});
const userObj = ref({});
import { onLoad } from "@dcloudio/uni-app";

onLoad((option) => {
  const obj = uni.getStorageSync("USER__INFO__");
  userObj.value = {
    businessUnitIdName: obj.businessUnitIdName,
    postName: obj.postName,
    name: obj.name,
  };
  return getOtcWeekReportDetail({ id: option.id })
    .then((res) => {
      if (res.code === 0 && res.data) {
        tableList.value = res.data.OtcWeekReportTaskDetailVos.map((e) => {
          return {
            ...e,
            targetNum: e.targetNum || 0,
          };
        });
        comment.value = res.data.comment;
        let endDay = res.data.endDay.replaceAll("-", ".");
        let startDay = res.data.startDay.replaceAll("-", ".");
        timeWeek.value = startDay + "-" + endDay;
      }
    })
    .catch((error) => {
      console.error("Error:", error);
      throw error; // 将错误抛出，供外部捕获
    });
});
const 

// 确定职位
</script>

<style scoped lang="scss">
@import "/static/sass/global.scss";
.header-title {
  margin-bottom: 10rpx;
  .name {
    @include setBoldFont(28rpx, 36rpx, #1d1d1d);
  }
  .user-tag {
    @include setBoldFont(20rpx, 38rpx, $uni-text-color-inverse);
    border-radius: 8rpx;
    height: 38rpx;
    min-width: 50rpx;
    margin-left: 20rpx;
    text-align: center;
    padding: 3rpx 8rpx;
    background: #21c369;
  }
}

.area {
  color: var(---Gray6, #8d9094);
  font-size: 20rpx;
}

.clerkInfoCollect-panel {
  @include globalPageStyle();
  padding: 28rpx 20rpx 0;
  overflow-y: scroll;

  .button-groups {
    margin-bottom: 20rpx;

    .button-item {
      @include cardBgCommonStyle();
      margin-bottom: 20rpx;
      overflow: hidden;

      .setting-text {
        @include setlightFont();
      }

      padding: 20rpx;
    }
  }
}

.first-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24rpx;
  color: #000;
}

.from-title {
  @include setBoldFont(28rpx, 44rpx, #2f3133);
  margin: 20rpx 0;
}

.table {
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.9);
  padding: 20rpx;
  padding-top: 0;
  /* 橙色投影 */
  border: 1px solid #eeeff0;
}

.header,
.tableData {
  display: flex;
  justify-content: space-around;
  text-align: center;
  gap: 18rpx;
  color: var(---Gray6, #8d9094);
  font-size: 24rpx;
  line-height: 80rpx;

  view {
    flex: 1;
  }

  .name {
    flex: 1;
    color: var(---Gray6, #8d9094) !important;
    font-size: 24rpx !important;
    text-align: left;
  }

  .title {
    text-align: left;
  }
}

.tableData {
  @include setBoldFont(28rpx, 36rpx, #2f3133);
  line-height: 40px;
}

.btnNext {
  height: 82rpx;
  text-align: center;
  background: linear-gradient(180deg, #ffbf1a -33.33%, #e55f14 131.94%);
  //box-shadow: 0.0625rem 0.0625rem 0.25rem 0 #f3c66a;
  border-radius: 5px;
  @include absoluteHorizontalCenter();
  bottom: calc(env(safe-area-inset-bottom) + 140rpx);
  width: calc(100% - 30rpx);
  @include setBoldFont(28rpx, 82rpx, #fff);
  z-index: 10000;
}

.nextTurn {
  background: #ffffff;
  color: #f3c66a;
  box-shadow: 0.0625rem 0.0625rem 0.25rem 0 #f3c66a;
  bottom: calc(env(safe-area-inset-bottom) + 240rpx);
}
</style>
