import config from '@/common/config/index.js';
var bmap = require('../static/map/bmap-wx.min.js');
import reportWxLog from './log.js';
import {
	baiduToGaoDe,
} from '/common/api/line/index.js';
// 百度地图的经纬度转换成腾讯的经纬度
export const baiduToGaoDeFun = (params) => {
	return baiduToGaoDe(params)
		.then((Result) => {
			return Result; // 成功时返回结果
		})
		.catch((error) => {
			console.error('Error:', error);
			throw error; // 将错误抛出，供外部捕获
		});
};

const getGaodeLocation = async () => {
	try {
		const locationResult = await new Promise((resolve, reject) => {
			uni.getLocation({
				type: 'gcj02', // 使用高德地图坐标系
				success: (location) => {
					resolve({
						status: 'success',
						lat: location.latitude,
						lng: location.longitude,
					});
				},
				fail: (err) => {
					resolve({
						status: 'fail',
						error: err,
					});
				},
			});
		});

		return locationResult;
	} catch (error) {
		return {
			status: 'fail',
			error,
		};
	}
};

/**
 * 获取用户本地化信息，包括百度和高德经纬度信息
 * @param {Function} onSuccess - 成功回调，返回处理后的位置信息
 * @param {Function} onFail - 失败回调，返回错误信息
 */
export const fetchUserLocation = (onSuccess, onFail) => {
	// 创建百度地图对象，使用指定的 API Key
	const BMap = new bmap.BMapWX({
		ak: config.mapKey
	});
	// 调用百度地图逆地理编码接口
	BMap.regeocoding({
		fail: (err) => {
			console.error('获取位置失败:', err);
			if (onFail) onFail(err);
		},
		success: async (res) => {
			try {
				const {
					result,
					status
				} = res.originalData;
				if (status !== 0) {
					reportWxLog.error('百度服务位置服务返回异常状态')
					reportWxLog.error(result)
				}
				if (status !== 0) throw new Error('位置服务返回异常状态');

				console.log(result, '百度地图定位---result');
				const {
					location,
					formatted_address_poi
				} = result;

				// 调用百度转高德接口
				const locationResult = await getGaodeLocation();
				console.log(locationResult, '---微信的坐标');
				const {
					lat,
					lng
				} = locationResult;
				if (locationResult.status == 'fail') {
					throw new Error('坐标转换失败');
					reportWxLog.error('baiduToGaoDe坐标转换失败')
					reportWxLog.error(locationResult)
					if (onFail) onFail(err);
				} else {
					if (onSuccess) {
						onSuccess({
							baidu: {
								latitude: location.lat,
								longitude: location.lng
							},
							gaode: {
								latitude: lat,
								longitude: lng
							},
							address: formatted_address_poi,
						});
					}
				}
			} catch (error) {
				console.error('处理位置数据失败:', error);
				if (onFail) onFail(error);
			}
		},
	});
};